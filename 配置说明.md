# 展车控制应用配置说明

## 概述

本应用现在支持两种不同的构建变体，可以根据设备类型编译出不同功能的版本：

- **手机版（发送方）**：安装在手机上，用于发送控制指令
- **树莓派版（接收方）**：安装在树莓派上，用于接收和处理控制指令

## 构建变体

### 手机版 (phone)
- **应用ID**: `com.example.exhibition_car_control.phone`
- **功能特点**:
  - 显示蓝牙客户端功能（发送方）
  - 显示设备扫描功能
  - 显示数据发送功能
  - 隐藏蓝牙服务器功能
  - 隐藏消息接收功能
  - 主题色：蓝色

### 树莓派版 (raspberrypi)
- **应用ID**: `com.example.exhibition_car_control.raspberrypi`
- **功能特点**:
  - 显示蓝牙服务器功能（接收方）
  - 显示消息接收功能
  - 自动启动统一蓝牙服务器
  - 隐藏设备扫描功能
  - 隐藏数据发送功能
  - 主题色：绿色

## 构建方法

### 方法一：使用批处理脚本（推荐）

1. **构建手机版**:
   ```bash
   build-phone.bat
   ```

2. **构建树莓派版**:
   ```bash
   build-raspberrypi.bat
   ```

3. **构建所有版本**:
   ```bash
   build-all.bat
   ```

### 方法二：使用Gradle命令

1. **构建手机版**:
   ```bash
   gradlew assemblePhoneDebug      # Debug版本
   gradlew assemblePhoneRelease    # Release版本
   ```

2. **构建树莓派版**:
   ```bash
   gradlew assembleRaspberrypiDebug    # Debug版本
   gradlew assembleRaspberrypiRelease  # Release版本
   ```

## 输出文件位置

构建完成后，APK文件将生成在以下位置：

```
app/build/outputs/apk/
├── phone/
│   ├── debug/app-phone-debug.apk
│   └── release/app-phone-release.apk
└── raspberrypi/
    ├── debug/app-raspberrypi-debug.apk
    └── release/app-raspberrypi-release.apk
```

## 安装方法

### 方法一：使用安装脚本（推荐）

1. **安装手机版**:
   ```bash
   install-phone.bat
   ```

2. **安装树莓派版**:
   ```bash
   install-raspberrypi.bat
   ```

### 方法二：使用ADB命令

1. **安装手机版**:
   ```bash
   adb install -r app\build\outputs\apk\phone\debug\app-phone-debug.apk
   ```

2. **安装树莓派版**:
   ```bash
   adb install -r app\build\outputs\apk\raspberrypi\debug\app-raspberrypi-debug.apk
   ```

### 前提条件

- 设备已启用开发者选项和USB调试
- 已安装ADB工具
- 设备通过USB连接到电脑

## 配置文件说明

### DeviceConfig.kt

位置：`app/src/main/java/com/example/exhibition_car_control/config/DeviceConfig.kt`

这个文件包含了所有的配置逻辑：

- **设备类型检测**：根据BuildConfig自动识别当前版本
- **功能开关**：控制不同功能模块的显示和启用
- **UI配置**：不同版本的主题色和文本
- **调试配置**：调试信息的显示控制

### 主要配置项

```kotlin
// 功能配置
showBluetoothClient: Boolean     // 是否显示蓝牙客户端功能
showBluetoothServer: Boolean     // 是否显示蓝牙服务器功能
showDeviceScanning: Boolean      // 是否显示设备扫描功能
showDataSending: Boolean         // 是否显示数据发送功能
showMessageReceiving: Boolean    // 是否显示消息接收功能
autoStartServer: Boolean         // 是否自动启动服务器
```

## 使用场景

### 手机版使用场景
1. 安装在手机上
2. 启动应用后可以扫描附近的蓝牙设备
3. 连接到树莓派设备
4. 发送控制指令

### 树莓派版使用场景
1. 安装在树莓派上
2. 启动应用后自动启动蓝牙服务器
3. 等待手机连接
4. 接收并处理控制指令

## 注意事项

1. **权限要求**：两个版本都需要蓝牙相关权限
2. **网络要求**：无需网络连接，使用蓝牙通信
3. **兼容性**：支持经典蓝牙和BLE（蓝牙低功耗）
4. **调试模式**：Debug版本会显示额外的调试信息

## 自定义配置

如果需要修改配置，可以编辑 `DeviceConfig.kt` 文件中的相关设置。例如：

- 修改主题色
- 调整功能开关
- 更改自动启动行为
- 自定义UI文本

## 故障排除

1. **构建失败**：确保已安装JDK 11和Android SDK
2. **权限问题**：确保应用已获得蓝牙权限
3. **连接问题**：检查设备蓝牙是否已启用
4. **版本混淆**：检查应用ID确认安装的是正确版本
