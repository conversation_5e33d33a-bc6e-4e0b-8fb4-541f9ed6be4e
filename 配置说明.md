# 简单配置说明

## 🎯 超简单使用方法

1. 打开文件：`app/src/main/java/com/example/exhibition_car_control/config/DeviceConfig.kt`

2. 找到这一行：
   ```kotlin
   private val DEVICE_TYPE = DeviceType.PHONE  // 改成 DeviceType.RASPBERRY_PI 就是树莓派版
   ```

3. 修改配置：
   - **手机版（发送方）**：`DeviceType.PHONE`
   - **树莓派版（接收方）**：`DeviceType.RASPBERRY_PI`

4. 在Android Studio中运行应用

## 📱 两种版本的区别

### 手机版 (PHONE)
- ✅ 显示设备扫描功能（扫描BLE按键设备）
- ✅ 显示连接功能（连接BLE按键设备）
- ✅ 显示数据发送功能（发送数据到树莓派）
- ❌ 隐藏服务器功能
- 🎨 主题色：蓝色

### 树莓派版 (RASPBERRY_PI)
- ✅ 显示设备扫描功能（扫描BLE按键设备）
- ✅ 显示连接功能（连接BLE按键设备）
- ❌ 隐藏数据发送功能（不发送数据到其他设备）
- ✅ 显示服务器功能（接收手机发送的消息）
- ✅ 自动启动蓝牙服务器
- 🎨 主题色：绿色

**重要说明**：两个版本都可以连接BLE按键设备，因为按键设备需要通过客户端模式连接！

## 🔧 就这么简单！

现在你只需要：
1. 改一行代码中的配置
2. 在Android Studio中运行
3. 应用会自动显示对应的功能

不需要复杂的构建脚本，不需要多个APK文件，一个配置搞定！
