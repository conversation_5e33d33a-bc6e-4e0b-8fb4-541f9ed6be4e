# 版本信息

## 配置系统版本

**版本**: 1.0.0  
**发布日期**: 2025-08-02  
**作者**: Augment Agent  

## 更新内容

### v1.0.0 (2025-08-02)

#### 新增功能
- ✅ 添加了产品变体配置系统
- ✅ 支持手机版和树莓派版两种构建变体
- ✅ 创建了DeviceConfig配置管理类
- ✅ 实现了基于配置的功能显示/隐藏
- ✅ 添加了自动启动服务器功能（树莓派版）
- ✅ 创建了构建脚本和安装脚本
- ✅ 添加了配置测试脚本

#### 配置特性
- 🔧 **手机版配置**:
  - 显示蓝牙客户端功能
  - 显示设备扫描功能
  - 显示数据发送功能
  - 隐藏蓝牙服务器功能
  - 主题色：蓝色 (#2196F3)

- 🔧 **树莓派版配置**:
  - 显示蓝牙服务器功能
  - 显示消息接收功能
  - 自动启动统一蓝牙服务器
  - 隐藏设备扫描功能
  - 隐藏数据发送功能
  - 主题色：绿色 (#4CAF50)

#### 构建系统
- 📦 **构建脚本**:
  - `build-phone.bat` - 构建手机版
  - `build-raspberrypi.bat` - 构建树莓派版
  - `build-all.bat` - 构建所有版本
  - `test-config.bat` - 测试配置

- 📱 **安装脚本**:
  - `install-phone.bat` - 安装手机版
  - `install-raspberrypi.bat` - 安装树莓派版
  - `deploy-workflow.bat` - 完整工作流程

#### 文档
- 📚 创建了详细的配置说明文档
- 📚 更新了README文件
- 📚 添加了自定义配置示例

## 技术实现

### Gradle配置
- 使用`productFlavors`实现构建变体
- 通过`buildConfigField`传递配置参数
- 使用`resValue`设置不同的资源值

### 代码架构
- `DeviceConfig` - 主配置类
- `CustomConfig.example` - 自定义配置示例
- 条件编译 - 基于BuildConfig的功能控制

### 应用ID区分
- 手机版: `com.example.exhibition_car_control.phone`
- 树莓派版: `com.example.exhibition_car_control.raspberrypi`

## 使用说明

### 快速开始
1. 运行 `test-config.bat` 测试配置
2. 运行 `deploy-workflow.bat` 选择构建和安装选项
3. 或直接运行对应的构建脚本

### 自定义配置
1. 复制 `CustomConfig.kt.example` 为 `CustomConfig.kt`
2. 修改配置参数
3. 在 `DeviceConfig.kt` 中引用自定义配置

## 兼容性

- **Android版本**: API 21+ (Android 5.0+)
- **Gradle版本**: 8.0+
- **Kotlin版本**: 1.8+
- **Java版本**: 11+

## 已知问题

- 无

## 计划功能

- [ ] 添加更多自定义配置选项
- [ ] 支持更多设备类型
- [ ] 添加配置验证功能
- [ ] 支持运行时配置切换

## 支持

如有问题或建议，请查看：
- [配置说明.md](配置说明.md) - 详细配置文档
- [README.md](README.md) - 项目概述
- 源代码注释 - 代码级别的说明
