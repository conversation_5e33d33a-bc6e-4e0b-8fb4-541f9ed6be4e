package com.example.exhibition_car_control

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.content.Intent
import android.os.Build
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.rememberMultiplePermissionsState
import com.zerosense.bluetooth.*
import com.example.exhibition_car_control.config.DeviceConfig
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun BluetoothDemoScreen(modifier: Modifier = Modifier) {
    val context = LocalContext.current
    
    // SDK 初始化状态
    var sdkInitialized by remember { mutableStateOf(false) }
    var bluetoothManager by remember { mutableStateOf<BluetoothManager?>(null) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var successMessage by remember { mutableStateOf<String?>(null) }
    var infoMessage by remember { mutableStateOf<String?>(null) }

    // 初始化 SDK
    LaunchedEffect(Unit) {
        try {
            ZeroSenseBluetoothSDK.initialize(context)
            bluetoothManager = ZeroSenseBluetoothSDK.getBluetoothManager()
            sdkInitialized = true
        } catch (e: Exception) {
            errorMessage = "SDK初始化失败: ${e.message}"
            sdkInitialized = true // 即使失败也标记为已尝试初始化
        }
    }

    // 状态管理
    var scanState by remember { mutableStateOf(BluetoothScanState.IDLE) }
    var discoveredDevices by remember { mutableStateOf<List<BluetoothDeviceInfo>>(emptyList()) }
    var pairedDevices by remember { mutableStateOf<List<BluetoothDeviceInfo>>(emptyList()) }
    var connectedDevices by remember { mutableStateOf<List<BluetoothDeviceInfo>>(emptyList()) }
    var deviceConnectionStates by remember { mutableStateOf<Map<String, BluetoothConnectionState>>(emptyMap()) }

    // 服务器相关状态
    var isServerRunning by remember { mutableStateOf(false) }
    var isUnifiedServer by remember { mutableStateOf(false) } // 是否使用统一服务器
    var receivedMessages by remember { mutableStateOf<List<ReceivedMessage>>(emptyList()) }
    
    // 权限管理
    val permissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        arrayOf(
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
    } else {
        arrayOf(
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
    }
    
    val permissionsState = rememberMultiplePermissionsState(permissions.toList())
    
    // 蓝牙启用请求
    val enableBluetoothLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        // 处理蓝牙启用结果
    }
    
    // SDK 回调
    val callback = remember {
        object : BluetoothCallback {
            override fun onScanStateChanged(state: BluetoothScanState) {
                scanState = state
            }
            
            override fun onDeviceFound(device: BluetoothDeviceInfo) {
                // 只有未配对的设备才添加到已发现设备列表
                if (!device.isPaired) {
                    discoveredDevices = discoveredDevices.toMutableList().apply {
                        removeAll { it.address == device.address }
                        add(device)
                    }
                }
            }
            
            override fun onConnectionStateChanged(device: BluetoothDeviceInfo, state: BluetoothConnectionState) {
                // 更新连接状态
                deviceConnectionStates = deviceConnectionStates.toMutableMap().apply {
                    this[device.address] = state
                }

                when (state) {
                    BluetoothConnectionState.CONNECTED -> {
                        // 设备配对成功，更新各个列表
                        val updatedDevice = device.copy(isConnected = true, isPaired = true)

                        // 添加到已配对设备列表（也就是已连接设备列表）
                        pairedDevices = pairedDevices.toMutableList().apply {
                            removeAll { it.address == device.address }
                            add(updatedDevice)
                        }

                        // 从已发现设备列表中移除
                        discoveredDevices = discoveredDevices.filter { it.address != device.address }

                        // 更新已连接设备列表（实际上就是已配对设备）
                        connectedDevices = pairedDevices
                    }
                    BluetoothConnectionState.DISCONNECTED -> {
                        // 设备断开连接（取消配对或连接失败）
                        pairedDevices = pairedDevices.filter { it.address != device.address }
                        connectedDevices = pairedDevices
                    }
                    else -> {
                        // 处理其他连接状态（如正在连接）
                    }
                }
            }
            
            override fun onError(error: String) {
                errorMessage = error
                successMessage = null // 清除成功消息
                infoMessage = null // 清除信息提示
            }

            override fun onSuccess(message: String) {
                successMessage = message
                errorMessage = null // 清除错误消息
                infoMessage = null // 清除信息提示
            }

            override fun onInfo(message: String) {
                infoMessage = message
                errorMessage = null // 清除错误消息
                successMessage = null // 清除成功消息
            }

            override fun onPermissionRequired(permissions: Array<String>) {
                // 权限已通过 Accompanist 处理
            }

            override fun onDataReceived(senderAddress: String, data: String) {
                // 添加接收到的消息
                receivedMessages = receivedMessages + ReceivedMessage(senderAddress, data)
            }

            override fun onCharacteristicNotification(deviceAddress: String, characteristicUuid: String, data: ByteArray) {
                // 处理BLE特征值通知
                val dataString = data.joinToString(" ") { "%02X".format(it) }
                val message = "BLE通知 [${characteristicUuid}]: $dataString"
                receivedMessages = receivedMessages + ReceivedMessage(deviceAddress, message)
            }

            override fun onBleConnectionStateChanged(deviceAddress: String, state: Int) {
                // 处理BLE连接状态变化
                val stateString = when (state) {
                    2 -> "已连接" // BluetoothProfile.STATE_CONNECTED
                    0 -> "已断开" // BluetoothProfile.STATE_DISCONNECTED
                    1 -> "正在连接" // BluetoothProfile.STATE_CONNECTING
                    3 -> "正在断开" // BluetoothProfile.STATE_DISCONNECTING
                    else -> "未知状态($state)"
                }
                infoMessage = "BLE设备 $deviceAddress $stateString"
            }
        }
    }
    
    // 注册回调并初始化已配对设备
    LaunchedEffect(bluetoothManager) {
        bluetoothManager?.addCallback(callback)
        // 初始化已配对设备列表
        bluetoothManager?.let { manager ->
            pairedDevices = manager.getPairedDevices().map { device ->
                device.copy(isConnected = true) // 已配对设备视为已连接
            }
            connectedDevices = pairedDevices
            // 初始化服务器状态
            isServerRunning = manager.isServerRunning()

            // 树莓派版本自动启动服务器
            if (DeviceConfig.Features.autoStartServer && !isServerRunning) {
                if (manager.startUnifiedBluetoothServer()) {
                    isServerRunning = true
                    isUnifiedServer = true
                    successMessage = "树莓派版本已自动启动统一蓝牙服务器"
                }
            }
        }
    }
    
    // 清理回调
    DisposableEffect(bluetoothManager) {
        onDispose {
            bluetoothManager?.removeCallback(callback)
        }
    }
    
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 标题
        item {
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = DeviceConfig.UI.mainTitle,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(DeviceConfig.UI.primaryColor)
                )
                Text(
                    text = DeviceConfig.UI.subtitle,
                    fontSize = 16.sp,
                    color = Color.Gray
                )

                // 调试信息（仅在调试模式下显示）
                if (BuildConfig.DEBUG) {
                    Card(
                        colors = CardDefaults.cardColors(containerColor = Color.Yellow.copy(alpha = 0.1f))
                    ) {
                        Text(
                            text = "调试信息:\n设备类型: ${DeviceConfig.currentDeviceType.name}",
                            modifier = Modifier.padding(8.dp),
                            fontSize = 12.sp,
                            color = Color(0xFF795548)
                        )
                    }
                }
            }
        }

        // 错误信息显示
        errorMessage?.let { error ->
            item {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.1f))
                ) {
                    Text(
                        text = "错误: $error",
                        modifier = Modifier.padding(12.dp),
                        color = Color.Red
                    )
                }
            }
        }

        // 成功信息显示
        successMessage?.let { success ->
            item {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color.Green.copy(alpha = 0.1f))
                ) {
                    Text(
                        text = "✓ $success",
                        modifier = Modifier.padding(12.dp),
                        color = Color(0xFF006400) // 深绿色
                    )
                }
            }
        }

        // 信息提示显示
        infoMessage?.let { info ->
            item {
                Card(
                    colors = CardDefaults.cardColors(containerColor = Color.Blue.copy(alpha = 0.1f))
                ) {
                    Text(
                        text = "ℹ $info",
                        modifier = Modifier.padding(12.dp),
                        color = Color(0xFF1976D2) // 蓝色
                    )
                }
            }
        }

        // 权限检查
        if (!permissionsState.allPermissionsGranted) {
            item {
                Card {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text("需要蓝牙权限才能使用此功能")
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(
                            onClick = { permissionsState.launchMultiplePermissionRequest() }
                        ) {
                            Text("请求权限")
                        }
                    }
                }
            }
        } else {
            // SDK 初始化检查
            if (!sdkInitialized) {
                item {
                    Card {
                        Column(modifier = Modifier.padding(16.dp)) {
                            Text("正在初始化蓝牙SDK...")
                            Spacer(modifier = Modifier.height(8.dp))
                            CircularProgressIndicator()
                        }
                    }
                }
            } else if (bluetoothManager == null) {
                item {
                    Card {
                        Text(
                            text = "蓝牙SDK初始化失败",
                            modifier = Modifier.padding(16.dp),
                            color = Color.Red
                        )
                    }
                }
            } else {
                val manager = bluetoothManager
                if (manager == null || !manager.isBluetoothAvailable()) {
                    item {
                        Card {
                            Text(
                                text = "设备不支持蓝牙",
                                modifier = Modifier.padding(16.dp)
                            )
                        }
                    }
                } else if (!manager.isBluetoothEnabled()) {
                    item {
                        Card {
                            Column(modifier = Modifier.padding(16.dp)) {
                                Text("蓝牙未启用")
                                Spacer(modifier = Modifier.height(8.dp))
                                Button(
                                    onClick = {
                                        val enableBtIntent =
                                            Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
                                        enableBluetoothLauncher.launch(enableBtIntent)
                                    }
                                ) {
                                    Text("启用蓝牙")
                                }
                            }
                        }
                    }
                } else {
                    // 蓝牙服务器部分（仅在树莓派版本显示）
                    if (DeviceConfig.Features.showBluetoothServer) {
                        item {
                            UnifiedBluetoothServerSection(
                                bluetoothManager = manager,
                                isServerRunning = isServerRunning,
                                isUnifiedServer = isUnifiedServer,
                                receivedMessages = receivedMessages,
                                onStartClassicServer = {
                                    if (manager.startBluetoothServer()) {
                                        isServerRunning = true
                                        isUnifiedServer = false
                                    }
                                },
                                onStartUnifiedServer = {
                                    if (manager.startUnifiedBluetoothServer()) {
                                        isServerRunning = true
                                        isUnifiedServer = true
                                    }
                                },
                                onStopServer = {
                                    if (isUnifiedServer) {
                                        manager.stopUnifiedBluetoothServer()
                                    } else {
                                        manager.stopBluetoothServer()
                                    }
                                    isServerRunning = false
                                    isUnifiedServer = false
                                },
                                onClearMessages = {
                                    receivedMessages = emptyList()
                                }
                            )
                        }
                    }

                    // 蓝牙客户端部分（两个版本都显示，用于连接BLE按键设备）
                    if (DeviceConfig.Features.showBluetoothClient) {
                        item {
                            BluetoothControlSection(
                                bluetoothManager = manager,
                                scanState = scanState,
                                discoveredDevices = discoveredDevices,
                                pairedDevices = pairedDevices,
                                onRefreshPairedDevices = {
                                    pairedDevices = manager.getPairedDevices().map { device ->
                                        device.copy(isConnected = true) // 已配对设备视为已连接
                                    }
                                    connectedDevices = pairedDevices
                                },
                                deviceConnectionStates = deviceConnectionStates
                            )
                        }
                    }

                    // BLE功能部分（两个版本都显示）
                    item {
                        BleControlSection(
                            bluetoothManager = manager
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun BleControlSection(
    bluetoothManager: BluetoothManager
) {
    var isBleScanningState by remember { mutableStateOf(false) }
    var connectedBleDevices by remember { mutableStateOf<List<String>>(emptyList()) }

    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "BLE (蓝牙低功耗) 功能",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold
            )

            // BLE扫描控制
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        if (isBleScanningState) {
                            bluetoothManager.stopBleScan()
                            isBleScanningState = false
                        } else {
                            if (bluetoothManager.startBleScan()) {
                                isBleScanningState = true
                            }
                        }
                    },
                    modifier = Modifier.weight(1f)
                ) {
                    Text(if (isBleScanningState) "停止BLE扫描" else "开始BLE扫描")
                }
            }

            // 连接到BLE设备示例
            Text(
                text = "BLE设备连接示例",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )

            var deviceAddress by remember { mutableStateOf("") }

            OutlinedTextField(
                value = deviceAddress,
                onValueChange = { deviceAddress = it },
                label = { Text("BLE设备地址") },
                placeholder = { Text("例如: AA:BB:CC:DD:EE:FF") },
                modifier = Modifier.fillMaxWidth()
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        if (deviceAddress.isNotBlank()) {
                            bluetoothManager.connectToBleDevice(deviceAddress.trim())
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = deviceAddress.isNotBlank()
                ) {
                    Text("连接BLE设备")
                }

                Button(
                    onClick = {
                        if (deviceAddress.isNotBlank()) {
                            bluetoothManager.subscribeToFFE4Notification(deviceAddress.trim())
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = deviceAddress.isNotBlank()
                ) {
                    Text("订阅0xFFE4")
                }
            }

            // 快速连接按钮
            Button(
                onClick = {
                    if (deviceAddress.isNotBlank()) {
                        bluetoothManager.autoConnectAndSubscribeBleDevice(deviceAddress.trim())
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                enabled = deviceAddress.isNotBlank(),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF4CAF50))
            ) {
                Text("快速连接并订阅 (一键完成)", color = Color.White)
            }

            // 诊断按钮组
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = {
                        if (deviceAddress.isNotBlank()) {
                            bluetoothManager.diagnoseBleDevice(deviceAddress.trim())
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = deviceAddress.isNotBlank(),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFF9800))
                ) {
                    Text("设备诊断", color = Color.White)
                }

                Button(
                    onClick = {
                        if (deviceAddress.isNotBlank()) {
                            bluetoothManager.diagnoseBleConnectionIssues(deviceAddress.trim())
                        }
                    },
                    modifier = Modifier.weight(1f),
                    enabled = deviceAddress.isNotBlank(),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFE91E63))
                ) {
                    Text("连接诊断", color = Color.White)
                }
            }

            // 显示已连接的BLE设备
            LaunchedEffect(Unit) {
                connectedBleDevices = bluetoothManager.getConnectedBleDevices()
            }

            if (connectedBleDevices.isNotEmpty()) {
                Text(
                    text = "已连接的BLE设备:",
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium
                )

                connectedBleDevices.forEach { address ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = address,
                            fontSize = 12.sp,
                            modifier = Modifier.weight(1f)
                        )

                        Button(
                            onClick = {
                                bluetoothManager.disconnectBleDevice(address)
                                connectedBleDevices = bluetoothManager.getConnectedBleDevices()
                            },
                            colors = ButtonDefaults.buttonColors(containerColor = Color.Red)
                        ) {
                            Text("断开", color = Color.White)
                        }
                    }
                }
            }

            // 使用说明
            Text(
                text = """
                📖 使用说明:
                1. 输入BLE设备地址 (格式: AA:BB:CC:DD:EE:FF)
                2. 点击"快速连接并订阅"一键完成连接和订阅
                3. 或分步操作：先"连接BLE设备"，再"订阅0xFFE4"
                4. 接收到的按键信号将显示在上方消息列表中

                🔧 故障排除:
                • 连接失败：点击"连接诊断"查看详细原因
                • 找不到特征值：点击"设备诊断"检查设备兼容性
                • GATT错误133：通常是信号问题，请靠近设备重试
                """.trimIndent(),
                fontSize = 12.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@Composable
private fun UnifiedBluetoothServerSection(
    bluetoothManager: BluetoothManager,
    isServerRunning: Boolean,
    isUnifiedServer: Boolean,
    receivedMessages: List<ReceivedMessage>,
    onStartClassicServer: () -> Unit,
    onStartUnifiedServer: () -> Unit,
    onStopServer: () -> Unit,
    onClearMessages: () -> Unit
) {
    Card {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = if (DeviceConfig.isRaspberryPiVersion) "蓝牙服务器（接收方）" else "蓝牙服务器",
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(DeviceConfig.UI.primaryColor)
            )

            // 服务器状态显示
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = when {
                        !isServerRunning -> "服务器状态: 未启动"
                        isUnifiedServer -> "服务器状态: 统一服务器运行中"
                        else -> "服务器状态: 经典蓝牙服务器运行中"
                    },
                    fontSize = 14.sp
                )

                if (isServerRunning) {
                    Text(
                        text = "●",
                        color = Color.Green,
                        fontSize = 20.sp
                    )
                }
            }

            // 服务器控制按钮（根据配置显示）
            if (DeviceConfig.Features.showServerControls) {
                if (!isServerRunning) {
                    Column(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = onStartUnifiedServer,
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF4CAF50))
                        ) {
                            Text("启动统一蓝牙服务器 (推荐)", color = Color.White)
                        }

                        Text(
                            text = "统一服务器可同时接收经典蓝牙和BLE设备消息",
                            fontSize = 12.sp,
                            color = Color.Gray,
                            modifier = Modifier.padding(horizontal = 8.dp)
                        )

                        Button(
                            onClick = onStartClassicServer,
                            modifier = Modifier.fillMaxWidth(),
                            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF2196F3))
                        ) {
                            Text("启动经典蓝牙服务器", color = Color.White)
                        }
                    }
                } else {
                    Button(
                        onClick = onStopServer,
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(containerColor = Color.Red)
                    ) {
                        Text("停止服务器", color = Color.White)
                    }
                }
            } else if (isServerRunning) {
                // 即使不显示控制按钮，也要显示服务器状态
                Text(
                    text = "服务器已自动启动并运行中",
                    fontSize = 14.sp,
                    color = Color(0xFF4CAF50),
                    fontWeight = FontWeight.Medium
                )
            }

            // 消息列表
            if (receivedMessages.isNotEmpty()) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "接收到的消息 (${receivedMessages.size})",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )

                    Button(
                        onClick = onClearMessages,
                        colors = ButtonDefaults.buttonColors(containerColor = Color.Gray)
                    ) {
                        Text("清空", color = Color.White)
                    }
                }

                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    // 最新消息在最上面：反转列表顺序
                    items(receivedMessages.takeLast(50).reversed()) { message ->
                        MessageItemWithTime(message = message)
                    }
                }
            }

            // 使用说明
            Text(
                text = if (isUnifiedServer) {
                    "统一服务器正在运行，可以接收:\n• 经典蓝牙设备的SPP消息\n• BLE设备的特征值通知(如0xFFE4按键信号)"
                } else if (isServerRunning) {
                    "经典蓝牙服务器正在运行，只能接收SPP协议消息"
                } else {
                    "选择服务器类型:\n• 统一服务器: 同时支持经典蓝牙和BLE\n• 经典服务器: 仅支持经典蓝牙SPP协议"
                },
                fontSize = 12.sp,
                color = Color.Gray,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@Composable
fun MessageItemWithTime(message: ReceivedMessage) {
    val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    val timeString = timeFormat.format(Date(message.timestamp))

    Card(
        colors = CardDefaults.cardColors(containerColor = Color(0xFFF5F5F5))
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "来自: ${message.senderAddress}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                Text(
                    text = timeString,
                    fontSize = 12.sp,
                    color = Color.Gray,
                    fontWeight = FontWeight.Medium
                )
            }
            Text(
                text = message.data,
                fontSize = 14.sp,
                modifier = Modifier.padding(top = 2.dp)
            )
        }
    }
}
