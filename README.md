# ZeroSense 蓝牙 SDK

## 项目概述

这是一个为客户提供的蓝牙连接 SDK，支持蓝牙设备搜索、连接和静默连接功能。

## 🚀 构建配置

本项目现在支持两种构建变体：

### 📱 手机版（发送方）
- **应用ID**: `com.example.exhibition_car_control.phone`
- **功能**: 蓝牙客户端、设备扫描、数据发送
- **构建命令**: `build-phone.bat` 或 `gradlew assemblePhoneDebug`

### 🖥️ 树莓派版（接收方）
- **应用ID**: `com.example.exhibition_car_control.raspberrypi`
- **功能**: 蓝牙服务器、消息接收、自动启动
- **构建命令**: `build-raspberrypi.bat` 或 `gradlew assembleRaspberrypiDebug`

### 🔧 快速构建
```bash
# 构建手机版
build-phone.bat

# 构建树莓派版
build-raspberrypi.bat

# 构建所有版本
build-all.bat

# 测试配置
test-config.bat
```

详细配置说明请参考：[配置说明.md](配置说明.md)

## 项目结构

```
exhibition-car-control/
├── app/                    # Demo 应用（演示 SDK 使用方法）
├── bluetooth-sdk/          # 蓝牙 SDK 核心模块
│   ├── src/main/java/com/zerosense/bluetooth/
│   │   ├── ZeroSenseBluetoothSDK.kt      # SDK 入口类
│   │   ├── BluetoothManager.kt           # 蓝牙管理器
│   │   ├── BluetoothDeviceInfo.kt        # 设备信息数据类
│   │   ├── BluetoothCallback.kt          # 回调接口
│   │   ├── BluetoothConnectionState.kt   # 连接状态枚举
│   │   └── BluetoothScanState.kt         # 扫描状态枚举
│   └── build.gradle.kts
└── README.md
```

## SDK 功能特性

### 1. 蓝牙设备搜索
- 扫描附近的蓝牙设备
- 获取设备名称、地址、信号强度等信息
- 支持扫描状态监听

### 2. 蓝牙设备连接
- 连接到指定的蓝牙设备
- 支持连接状态监听
- 自动处理连接超时

### 3. 静默连接功能
- 自动连接已配对的设备
- 支持批量连接所有已配对设备
- 无需用户干预的后台连接

### 4. 设备管理
- 获取已配对设备列表
- 获取已连接设备列表
- 支持断开指定设备或所有设备

## SDK 集成指南

### 1. 添加依赖

在您的 `build.gradle.kts` 文件中添加：

```kotlin
dependencies {
    implementation(project(":bluetooth-sdk"))
    // 或者使用 AAR 文件
    // implementation files('libs/bluetooth-sdk-1.0.0.aar')
}
```

### 2. 添加权限

在您的 `AndroidManifest.xml` 中添加必要权限：

```xml
<!-- 蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

<!-- Android 12+ 新的蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />

<!-- 位置权限（蓝牙扫描需要） -->
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

<!-- 声明蓝牙功能 -->
<uses-feature
    android:name="android.hardware.bluetooth"
    android:required="true" />
```

### 3. 初始化 SDK

```kotlin
// 在 Application 或 Activity 中初始化
ZeroSenseBluetoothSDK.initialize(context)

// 获取蓝牙管理器
val bluetoothManager = ZeroSenseBluetoothSDK.getBluetoothManager()
```

### 4. 实现回调接口

```kotlin
val callback = object : BluetoothCallback {
    override fun onScanStateChanged(state: BluetoothScanState) {
        // 处理扫描状态变化
    }
    
    override fun onDeviceFound(device: BluetoothDeviceInfo) {
        // 处理发现新设备
    }
    
    override fun onConnectionStateChanged(device: BluetoothDeviceInfo, state: BluetoothConnectionState) {
        // 处理连接状态变化
    }
    
    override fun onError(error: String) {
        // 处理错误
    }
    
    override fun onPermissionRequired(permissions: Array<String>) {
        // 处理权限请求
    }
}

// 注册回调
bluetoothManager?.addCallback(callback)
```

### 5. 基本使用示例

```kotlin
// 检查蓝牙可用性
if (bluetoothManager?.isBluetoothAvailable() == true && 
    bluetoothManager.isBluetoothEnabled()) {
    
    // 开始扫描
    bluetoothManager.startScan()
    
    // 连接设备
    bluetoothManager.connectToDevice("设备地址")
    
    // 静默连接已配对设备
    bluetoothManager.connectToPairedDevicesAutomatically()
}

// 清理资源
bluetoothManager?.removeCallback(callback)
```

## API 参考

### ZeroSenseBluetoothSDK

| 方法 | 描述 |
|------|------|
| `initialize(context)` | 初始化 SDK |
| `getBluetoothManager()` | 获取蓝牙管理器实例 |
| `isInitialized()` | 检查是否已初始化 |
| `release()` | 释放 SDK 资源 |
| `getVersion()` | 获取 SDK 版本 |

### BluetoothManager

| 方法 | 描述 |
|------|------|
| `startScan()` | 开始扫描蓝牙设备 |
| `stopScan()` | 停止扫描 |
| `connectToDevice(address)` | 连接到指定设备 |
| `disconnectFromDevice(address)` | 断开指定设备 |
| `connectToPairedDevicesAutomatically()` | 静默连接所有已配对设备 |
| `connectToPairedDeviceAutomatically(address)` | 静默连接指定已配对设备 |
| `getDiscoveredDevices()` | 获取已发现设备列表 |
| `getPairedDevices()` | 获取已配对设备列表 |
| `getConnectedDevices()` | 获取已连接设备列表 |
| `isDeviceConnected(address)` | 检查设备是否已连接 |

## 运行 Demo

1. 打开项目
2. 运行 `app` 模块
3. 授予必要的蓝牙和位置权限
4. 使用界面测试各种蓝牙功能

## 注意事项

1. **权限管理**：确保在使用前请求并获得所有必要权限
2. **蓝牙状态**：使用前检查蓝牙是否可用和已启用
3. **线程安全**：SDK 内部处理了线程切换，回调在主线程执行
4. **资源释放**：应用退出时调用 `release()` 方法释放资源
5. **Android 版本兼容**：SDK 支持 Android API 35+，自动处理不同版本的权限差异

## 技术支持

如有问题，请联系开发团队。

## 版本历史

- v1.0.0: 初始版本，支持基本的蓝牙搜索、连接和静默连接功能
