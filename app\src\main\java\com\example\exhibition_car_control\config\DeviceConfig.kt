package com.example.exhibition_car_control.config

import com.example.exhibition_car_control.BuildConfig

/**
 * 设备配置类
 * 根据构建变体控制不同功能的显示和启用
 */
object DeviceConfig {
    
    /**
     * 设备类型枚举
     */
    enum class DeviceType {
        PHONE,           // 手机版（发送方）
        RASPBERRY_PI     // 树莓派版（接收方）
    }
    
    /**
     * 当前设备类型
     */
    val currentDeviceType: DeviceType = when (BuildConfig.DEVICE_TYPE) {
        "PHONE" -> DeviceType.PHONE
        "RASPBERRY_PI" -> DeviceType.RASPBERRY_PI
        else -> DeviceType.PHONE // 默认为手机版
    }
    
    /**
     * 是否为手机版
     */
    val isPhoneVersion: Boolean = BuildConfig.IS_PHONE_VERSION
    
    /**
     * 是否为树莓派版
     */
    val isRaspberryPiVersion: Boolean = BuildConfig.IS_RASPBERRY_PI_VERSION
    
    /**
     * 功能配置
     */
    object Features {
        
        /**
         * 是否显示蓝牙客户端功能（发送方功能）
         * 手机版：显示
         * 树莓派版：隐藏
         */
        val showBluetoothClient: Boolean = isPhoneVersion
        
        /**
         * 是否显示蓝牙服务器功能（接收方功能）
         * 手机版：隐藏
         * 树莓派版：显示
         */
        val showBluetoothServer: Boolean = isRaspberryPiVersion
        
        /**
         * 是否显示BLE功能
         * 手机版：显示（用于连接BLE设备）
         * 树莓派版：显示（用于接收BLE数据）
         */
        val showBleFeatures: Boolean = true
        
        /**
         * 是否显示设备扫描功能
         * 手机版：显示（需要扫描并连接其他设备）
         * 树莓派版：隐藏（只需要等待连接）
         */
        val showDeviceScanning: Boolean = isPhoneVersion
        
        /**
         * 是否显示数据发送功能
         * 手机版：显示
         * 树莓派版：隐藏
         */
        val showDataSending: Boolean = isPhoneVersion
        
        /**
         * 是否显示消息接收功能
         * 手机版：隐藏
         * 树莓派版：显示
         */
        val showMessageReceiving: Boolean = isRaspberryPiVersion
        
        /**
         * 是否自动启动服务器
         * 手机版：否
         * 树莓派版：是
         */
        val autoStartServer: Boolean = isRaspberryPiVersion
        
        /**
         * 是否显示服务器控制按钮
         * 手机版：否
         * 树莓派版：是
         */
        val showServerControls: Boolean = isRaspberryPiVersion
        
        /**
         * 默认服务器类型
         * 手机版：不适用
         * 树莓派版：统一服务器
         */
        val defaultServerType: ServerType = if (isRaspberryPiVersion) {
            ServerType.UNIFIED
        } else {
            ServerType.NONE
        }
    }
    
    /**
     * 服务器类型枚举
     */
    enum class ServerType {
        NONE,           // 不启动服务器
        CLASSIC,        // 经典蓝牙服务器
        UNIFIED         // 统一服务器（经典蓝牙 + BLE）
    }
    
    /**
     * UI配置
     */
    object UI {
        
        /**
         * 主标题
         */
        val mainTitle: String = when (currentDeviceType) {
            DeviceType.PHONE -> "蓝牙控制端"
            DeviceType.RASPBERRY_PI -> "蓝牙接收端"
        }
        
        /**
         * 副标题
         */
        val subtitle: String = when (currentDeviceType) {
            DeviceType.PHONE -> "发送控制指令到其他设备"
            DeviceType.RASPBERRY_PI -> "接收并处理控制指令"
        }
        
        /**
         * 主题色
         */
        val primaryColor: Long = when (currentDeviceType) {
            DeviceType.PHONE -> 0xFF2196F3      // 蓝色 - 手机版
            DeviceType.RASPBERRY_PI -> 0xFF4CAF50 // 绿色 - 树莓派版
        }
    }
    
    /**
     * 调试配置
     */
    object Debug {
        
        /**
         * 是否显示调试信息
         */
        val showDebugInfo: Boolean = BuildConfig.DEBUG
        
        /**
         * 是否启用详细日志
         */
        val enableVerboseLogging: Boolean = BuildConfig.DEBUG
        
        /**
         * 设备信息
         */
        fun getDeviceInfo(): String {
            return """
                设备类型: ${currentDeviceType.name}
                版本: ${BuildConfig.VERSION_NAME}
                构建类型: ${BuildConfig.BUILD_TYPE}
                应用ID: ${BuildConfig.APPLICATION_ID}
            """.trimIndent()
        }
    }
}
