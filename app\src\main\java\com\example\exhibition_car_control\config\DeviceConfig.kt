package com.example.exhibition_car_control.config

/**
 * 简单设备配置
 *
 * 🔧 使用方法：
 * 1. 修改下面的 DEVICE_TYPE 配置
 * 2. 在Android Studio中运行应用
 *
 * 📱 PHONE = 手机版（发送方）- 显示扫描、连接、发送功能
 * 🖥️ RASPBERRY_PI = 树莓派版（接收方）- 显示服务器、接收功能
 */
object DeviceConfig {

    // ⭐ 在这里修改设备类型 ⭐
    private val DEVICE_TYPE = DeviceType.PHONE  // 改成 DeviceType.RASPBERRY_PI 就是树莓派版

    enum class DeviceType {
        PHONE,           // 手机版（发送方）
        RASPBERRY_PI     // 树莓派版（接收方）
    }

    // 当前设备类型
    val currentDeviceType: DeviceType = DEVICE_TYPE

    // 是否为手机版
    val isPhoneVersion: Boolean = (DEVICE_TYPE == DeviceType.PHONE)

    // 是否为树莓派版
    val isRaspberryPiVersion: Boolean = (DEVICE_TYPE == DeviceType.RASPBERRY_PI)
    
    // 功能开关 - 根据设备类型自动配置
    object Features {
        val showBluetoothClient: Boolean = true                  // 两个版本都显示客户端功能（连接BLE按键设备需要）
        val showBluetoothServer: Boolean = isRaspberryPiVersion  // 显示服务器功能（接收消息）
        val showDeviceScanning: Boolean = true                   // 两个版本都显示设备扫描（扫描BLE按键设备需要）
        val showDataSending: Boolean = isPhoneVersion            // 显示数据发送（只有手机版发送数据到树莓派）
        val autoStartServer: Boolean = isRaspberryPiVersion      // 自动启动服务器
        val showServerControls: Boolean = isRaspberryPiVersion   // 显示服务器控制按钮
    }

    // UI配置
    object UI {
        val mainTitle: String = if (isPhoneVersion) "蓝牙控制端" else "蓝牙接收端"
        val subtitle: String = if (isPhoneVersion) "发送控制指令到其他设备" else "接收并处理控制指令"
        val primaryColor: Long = if (isPhoneVersion) 0xFF2196F3 else 0xFF4CAF50  // 蓝色/绿色
    }
}
