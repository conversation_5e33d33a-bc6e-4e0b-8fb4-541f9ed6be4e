@echo off
echo ========================================
echo 测试配置是否正确
echo ========================================

echo 正在检查Gradle配置...
call .\gradlew.bat help --quiet

if %ERRORLEVEL% EQU 0 (
    echo ✓ Gradle配置正确
    
    echo.
    echo 正在列出可用的构建任务...
    call .\gradlew.bat tasks --group=build --quiet
    
    echo.
    echo ========================================
    echo 配置测试完成！
    echo ========================================
    echo 可以使用以下命令构建不同版本：
    echo.
    echo 手机版：
    echo   .\gradlew.bat assemblePhoneDebug
    echo   .\gradlew.bat assemblePhoneRelease
    echo.
    echo 树莓派版：
    echo   .\gradlew.bat assembleRaspberrypiDebug
    echo   .\gradlew.bat assembleRaspberrypiRelease
    echo ========================================
) else (
    echo ✗ Gradle配置有问题，请检查build.gradle.kts文件
)

pause
