@echo off
echo ========================================
echo 安装树莓派版（接收方）
echo ========================================

set APK_DEBUG=app\build\outputs\apk\raspberrypi\debug\app-raspberrypi-debug.apk
set APK_RELEASE=app\build\outputs\apk\raspberrypi\release\app-raspberrypi-release.apk

echo 正在检查ADB连接...
adb devices

echo.
echo 请选择要安装的版本：
echo 1. Debug版本
echo 2. Release版本
echo 3. 退出
echo.

set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    if exist "%APK_DEBUG%" (
        echo 正在安装Debug版本...
        adb install -r "%APK_DEBUG%"
        if %ERRORLEVEL% EQU 0 (
            echo ✓ 树莓派版Debug版本安装成功！
            echo 应用ID: com.example.exhibition_car_control.raspberrypi
        ) else (
            echo ✗ 安装失败，请检查设备连接
        )
    ) else (
        echo ✗ Debug APK文件不存在，请先运行 build-raspberrypi.bat
    )
) else if "%choice%"=="2" (
    if exist "%APK_RELEASE%" (
        echo 正在安装Release版本...
        adb install -r "%APK_RELEASE%"
        if %ERRORLEVEL% EQU 0 (
            echo ✓ 树莓派版Release版本安装成功！
            echo 应用ID: com.example.exhibition_car_control.raspberrypi
        ) else (
            echo ✗ 安装失败，请检查设备连接
        )
    ) else (
        echo ✗ Release APK文件不存在，请先运行 build-raspberrypi.bat
    )
) else if "%choice%"=="3" (
    echo 退出安装
    goto :end
) else (
    echo 无效选择
)

:end
echo.
echo ========================================
pause
