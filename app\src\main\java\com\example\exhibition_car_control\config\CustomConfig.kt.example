package com.example.exhibition_car_control.config

/**
 * 自定义配置示例
 * 
 * 如果需要自定义配置，可以复制这个文件并重命名为 CustomConfig.kt
 * 然后在 DeviceConfig.kt 中引用这些自定义配置
 */
object CustomConfig {
    
    /**
     * 自定义UI配置
     */
    object CustomUI {
        
        // 自定义主题色
        val customPhoneColor: Long = 0xFF1976D2      // 深蓝色
        val customRaspberryPiColor: Long = 0xFF388E3C // 深绿色
        
        // 自定义标题
        val customPhoneTitle = "智能控制器"
        val customRaspberryPiTitle = "智能接收器"
        
        // 自定义副标题
        val customPhoneSubtitle = "发送控制指令到目标设备"
        val customRaspberryPiSubtitle = "接收并执行控制指令"
    }
    
    /**
     * 自定义功能配置
     */
    object CustomFeatures {
        
        // 是否启用高级调试功能
        val enableAdvancedDebug = true
        
        // 是否显示设备信息
        val showDeviceInfo = true
        
        // 是否启用自动重连
        val enableAutoReconnect = true
        
        // 自动重连间隔（毫秒）
        val autoReconnectInterval = 5000L
        
        // 最大重连次数
        val maxReconnectAttempts = 3
    }
    
    /**
     * 自定义服务器配置
     */
    object CustomServer {
        
        // 默认服务器名称
        val defaultServerName = "ZeroSense-Server"
        
        // 是否启用服务器日志
        val enableServerLogging = true
        
        // 服务器超时时间（毫秒）
        val serverTimeout = 30000L
        
        // 最大客户端连接数
        val maxClientConnections = 5
    }
    
    /**
     * 自定义蓝牙配置
     */
    object CustomBluetooth {
        
        // 扫描超时时间（毫秒）
        val scanTimeout = 12000L
        
        // 连接超时时间（毫秒）
        val connectionTimeout = 10000L
        
        // 是否启用BLE扫描
        val enableBleScan = true
        
        // BLE扫描间隔（毫秒）
        val bleScanInterval = 1000L
        
        // 是否启用经典蓝牙扫描
        val enableClassicScan = true
    }
    
    /**
     * 自定义消息配置
     */
    object CustomMessage {
        
        // 消息历史记录最大数量
        val maxMessageHistory = 100
        
        // 是否启用消息时间戳
        val enableMessageTimestamp = true
        
        // 是否启用消息加密
        val enableMessageEncryption = false
        
        // 消息格式
        val messageFormat = "JSON" // 可选: "JSON", "XML", "PLAIN"
    }
    
    /**
     * 使用自定义配置的示例方法
     */
    fun applyCustomConfig() {
        // 这里可以添加应用自定义配置的逻辑
        // 例如：修改 DeviceConfig 中的某些值
        
        // 示例：如果需要在运行时动态修改配置
        // DeviceConfig.UI.primaryColor = CustomUI.customPhoneColor
    }
}

/**
 * 配置工厂类
 * 用于根据不同条件创建不同的配置
 */
object ConfigFactory {
    
    /**
     * 根据设备类型获取配置
     */
    fun getConfigForDevice(deviceType: DeviceConfig.DeviceType): Any {
        return when (deviceType) {
            DeviceConfig.DeviceType.PHONE -> createPhoneConfig()
            DeviceConfig.DeviceType.RASPBERRY_PI -> createRaspberryPiConfig()
        }
    }
    
    private fun createPhoneConfig(): PhoneConfig {
        return PhoneConfig(
            enableScanning = true,
            enableDataSending = true,
            enableServerMode = false,
            primaryColor = CustomConfig.CustomUI.customPhoneColor
        )
    }
    
    private fun createRaspberryPiConfig(): RaspberryPiConfig {
        return RaspberryPiConfig(
            enableScanning = false,
            enableDataSending = false,
            enableServerMode = true,
            autoStartServer = true,
            primaryColor = CustomConfig.CustomUI.customRaspberryPiColor
        )
    }
}

/**
 * 手机配置数据类
 */
data class PhoneConfig(
    val enableScanning: Boolean,
    val enableDataSending: Boolean,
    val enableServerMode: Boolean,
    val primaryColor: Long
)

/**
 * 树莓派配置数据类
 */
data class RaspberryPiConfig(
    val enableScanning: Boolean,
    val enableDataSending: Boolean,
    val enableServerMode: Boolean,
    val autoStartServer: Boolean,
    val primaryColor: Long
)
