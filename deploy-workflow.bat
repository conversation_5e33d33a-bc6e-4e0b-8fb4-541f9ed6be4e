@echo off
echo ========================================
echo 展车控制应用部署工作流程
echo ========================================

echo 请选择操作：
echo 1. 构建并安装手机版（发送方）
echo 2. 构建并安装树莓派版（接收方）
echo 3. 构建所有版本
echo 4. 仅构建手机版
echo 5. 仅构建树莓派版
echo 6. 测试配置
echo 7. 清理项目
echo 8. 退出
echo.

set /p choice=请输入选择 (1-8): 

if "%choice%"=="1" (
    echo ========================================
    echo 构建并安装手机版
    echo ========================================
    call build-phone.bat
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo 构建成功，开始安装...
        call install-phone.bat
    ) else (
        echo 构建失败，请检查错误信息
    )
) else if "%choice%"=="2" (
    echo ========================================
    echo 构建并安装树莓派版
    echo ========================================
    call build-raspberrypi.bat
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo 构建成功，开始安装...
        call install-raspberrypi.bat
    ) else (
        echo 构建失败，请检查错误信息
    )
) else if "%choice%"=="3" (
    echo ========================================
    echo 构建所有版本
    echo ========================================
    call build-all.bat
) else if "%choice%"=="4" (
    echo ========================================
    echo 仅构建手机版
    echo ========================================
    call build-phone.bat
) else if "%choice%"=="5" (
    echo ========================================
    echo 仅构建树莓派版
    echo ========================================
    call build-raspberrypi.bat
) else if "%choice%"=="6" (
    echo ========================================
    echo 测试配置
    echo ========================================
    call test-config.bat
) else if "%choice%"=="7" (
    echo ========================================
    echo 清理项目
    echo ========================================
    echo 正在清理...
    call .\gradlew.bat clean
    echo 清理完成！
) else if "%choice%"=="8" (
    echo 退出
    goto :end
) else (
    echo 无效选择
)

:end
echo.
echo ========================================
echo 工作流程完成
echo ========================================
pause
